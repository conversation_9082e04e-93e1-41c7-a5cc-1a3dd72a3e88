/* Global Styles */
:root {
    --primary-color: #1e88e5;
    --secondary-color: #0d47a1;
    --accent-color: #ffa000;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #495057;
    --heading-color: #212529;
    --border-color: #dee2e6;
    --success-color: #28a745;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    color: var(--heading-color);
    font-weight: 600;
    line-height: 1.3;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background-color: var(--accent-color);
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-color);
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Header Styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 15px 0;
    transition: var(--transition);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.logo span {
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    color: var(--dark-color);
    font-weight: 500;
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    cursor: pointer;
}

.hamburger span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--dark-color);
    margin: 5px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/porec.jpg') no-repeat center center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    padding: 0;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: white;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Introduction Section */
.intro {
    background-color: var(--light-color);
}

.intro-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.intro-text, .intro-image {
    flex: 1;
}

.intro-text p {
    margin-bottom: 20px;
}

.intro-image {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

/* Apartments Section */
.apartments {
    background-color: #fff;
}

.apartment-card {
    display: flex;
    margin-bottom: 60px;
    background-color: var(--light-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.apartment-image, .apartment-details {
    flex: 1;
}

.apartment-image {
    position: relative;
}

.image-slider {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.image-slider img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
    opacity: 0;
    transform: scale(1.05);
}

.image-slider img.active {
    display: block;
    position: relative;
    opacity: 1;
    z-index: 1;
    transform: scale(1);
}

/* Additional slider styles for random image rotation */
.image-slider {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    cursor: pointer; /* Indicates interactivity */
}

.apartment-details {
    padding: 30px;
}

.apartment-details h3 {
    font-size: 2rem;
    margin-bottom: 5px;
}

.apartment-location {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
}

.apartment-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.apartment-features span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.apartment-description {
    margin-bottom: 20px;
}

.apartment-highlights h4 {
    margin-bottom: 10px;
}

.apartment-highlights ul {
    list-style: disc;
    padding-left: 20px;
    margin-bottom: 20px;
}

.apartment-highlights li {
    margin-bottom: 5px;
}

.price {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.price span {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Amenities Section */
.amenities {
    background-color: var(--light-color);
}

.amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.amenity-card {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.amenity-card:hover {
    transform: translateY(-10px);
}

.amenity-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.amenity-card h3 {
    margin-bottom: 10px;
}

/* Location Section */
.location {
    background-color: #fff;
}

.location-content {
    display: flex;
    gap: 30px;
}

.map {
    flex: 1.5;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.location-details {
    flex: 1;
}

.location-info, .nearby-attractions {
    margin-bottom: 30px;
}

.location-info h3, .nearby-attractions h3 {
    margin-bottom: 15px;
}

.location-info p, .nearby-attractions li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.location-info i, .nearby-attractions i {
    color: var(--primary-color);
}

/* Gallery Section */
.gallery {
    background-color: var(--light-color);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    transform: translateY(100%);
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

/* Contact Section */
.contact {
    background-color: #fff;
}

.contact-wrapper {
    display: flex;
    gap: 50px;
}

.contact-info, .booking-form {
    flex: 1;
}

.contact-info h3, .booking-form h3 {
    margin-bottom: 20px;
}

.contact-details {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.guest-reviews {
    margin-top: 30px;
}

.guest-reviews h3 {
    margin-bottom: 15px;
}

.review {
    background-color: var(--light-color);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: var(--box-shadow);
}

.review p {
    font-style: italic;
    margin-bottom: 10px;
}

.review span {
    display: block;
    text-align: right;
    font-weight: 500;
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: 'Poppins', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 70px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.footer-logo h2 {
    color: white;
    margin-bottom: 15px;
}

.footer-logo span {
    color: var(--accent-color);
}

.footer-links h3,
.footer-info h3 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #adb5bd;
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
}

.footer-info p {
    margin-bottom: 10px;
    color: #adb5bd;
    line-height: 1.5;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .intro-content,
    .apartment-card,
    .location-content,
    .contact-wrapper {
        flex-direction: column;
    }

    .apartment-image {
        min-height: 300px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }
}

@media (max-width: 768px) {
    .nav-links {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: white;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        z-index: 999;
    }

    .nav-links.active {
        left: 0;
    }

    .hamburger {
        display: block;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }
}
