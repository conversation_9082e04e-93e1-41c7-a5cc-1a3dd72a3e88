// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Navigation Toggle
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger) {
        hamburger.addEventListener('click', function() {
            this.classList.toggle('active');
            navLinks.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav link
    const navItems = document.querySelectorAll('.nav-links a');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navLinks.classList.remove('active');
        });
    });

    // Sticky Header
    const header = document.querySelector('header');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.style.padding = '10px 0';
            header.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        } else {
            header.style.padding = '15px 0';
            header.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        }
    });

    // Active Navigation Link on Scroll
    const sections = document.querySelectorAll('section');
    const navLinks2 = document.querySelectorAll('.nav-links a');

    window.addEventListener('scroll', function() {
        let current = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;

            if (pageYOffset >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks2.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href').substring(1) === current) {
                link.classList.add('active');
            }
        });
    });

    // Image Sliders for Apartments - Random image rotation
    document.addEventListener('DOMContentLoaded', function() {
        // Get all sliders
        const sliders = document.querySelectorAll('.image-slider');

        // Process each slider
        sliders.forEach(function(slider) {
            // Get all slides in this slider
            const slides = slider.querySelectorAll('img');

            // Set initial state
            let currentSlide = 0;
            slides[0].classList.add('active');

            // Function to show a specific slide
            function showSlide(n) {
                // Hide all slides
                slides.forEach(function(slide) {
                    slide.classList.remove('active');
                    slide.style.display = 'none';
                });

                // Show the current slide
                slides[n].classList.add('active');
                slides[n].style.display = 'block';
            }

            // Initialize - show first slide
            showSlide(currentSlide);

            // Function to show a random slide
            function showRandomSlide() {
                // Get a random index different from the current one
                let randomIndex;
                do {
                    randomIndex = Math.floor(Math.random() * slides.length);
                } while (randomIndex === currentSlide && slides.length > 1);

                currentSlide = randomIndex;
                showSlide(currentSlide);
            }

            // Auto-change to random slide every 3-5 seconds (random timing for more natural feel)
            function setRandomInterval() {
                // Random time between 3 and 5 seconds
                const randomTime = Math.floor(Math.random() * 2000) + 3000;
                return setTimeout(function() {
                    showRandomSlide();
                    intervalId = setRandomInterval(); // Set next interval
                }, randomTime);
            }

            // Start the random rotation
            let intervalId = setRandomInterval();

            // Pause rotation when mouse is over the slider
            slider.addEventListener('mouseenter', function() {
                clearTimeout(intervalId);
            });

            // Resume rotation when mouse leaves the slider
            slider.addEventListener('mouseleave', function() {
                intervalId = setRandomInterval();
            });
        });
    });

    // Make sure sliders are initialized when page loads
    window.onload = function() {
        // Force a refresh of all sliders
        const sliders = document.querySelectorAll('.image-slider');
        sliders.forEach(function(slider) {
            const slides = slider.querySelectorAll('img');
            slides.forEach(function(slide, index) {
                if (index === 0) {
                    slide.classList.add('active');
                    slide.style.display = 'block';
                } else {
                    slide.classList.remove('active');
                    slide.style.display = 'none';
                }
            });
        });
    };

    // Gallery Lightbox
    const galleryItems = document.querySelectorAll('.gallery-item');

    galleryItems.forEach(item => {
        item.addEventListener('click', function() {
            const imgSrc = this.querySelector('img').src;
            const imgAlt = this.querySelector('img').alt;

            // Create lightbox elements
            const lightbox = document.createElement('div');
            lightbox.className = 'lightbox';

            const lightboxContent = document.createElement('div');
            lightboxContent.className = 'lightbox-content';

            const closeBtn = document.createElement('span');
            closeBtn.className = 'close-lightbox';
            closeBtn.innerHTML = '&times;';

            const img = document.createElement('img');
            img.src = imgSrc;
            img.alt = imgAlt;

            const caption = document.createElement('div');
            caption.className = 'lightbox-caption';
            caption.textContent = imgAlt;

            // Append elements
            lightboxContent.appendChild(closeBtn);
            lightboxContent.appendChild(img);
            lightboxContent.appendChild(caption);
            lightbox.appendChild(lightboxContent);
            document.body.appendChild(lightbox);

            // Prevent scrolling when lightbox is open
            document.body.style.overflow = 'hidden';

            // Close lightbox when clicking on close button or outside the image
            closeBtn.addEventListener('click', closeLightbox);
            lightbox.addEventListener('click', function(e) {
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });

            // Close lightbox function
            function closeLightbox() {
                document.body.removeChild(lightbox);
                document.body.style.overflow = 'auto';
            }

            // Close lightbox with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (document.querySelector('.lightbox')) {
                        closeLightbox();
                    }
                }
            });
        });
    });

    // Form Validation
    const bookingForm = document.getElementById('booking-form');

    if (bookingForm) {
        bookingForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const apartment = document.getElementById('apartment').value;
            const checkIn = document.getElementById('check-in').value;
            const checkOut = document.getElementById('check-out').value;
            const guests = document.getElementById('guests').value;

            // Simple validation
            if (!name || !email || !phone || !apartment || !checkIn || !checkOut || !guests) {
                alert('Please fill in all required fields.');
                return;
            }

            // Check if check-out date is after check-in date
            const checkInDate = new Date(checkIn);
            const checkOutDate = new Date(checkOut);

            if (checkOutDate <= checkInDate) {
                alert('Check-out date must be after check-in date.');
                return;
            }

            // Check if the number of guests is appropriate for the selected apartment
            if (apartment === 'luxury' && guests > 6) {
                alert('The Luxury Penthouse can accommodate a maximum of 6 guests.');
                return;
            } else if (apartment === 'comfort' && guests > 4) {
                alert('The Comfort Suite can accommodate a maximum of 4 guests.');
                return;
            } else if (apartment === 'cozy' && guests > 2) {
                alert('The Cozy Studio can accommodate a maximum of 2 guests.');
                return;
            }

            // If all validations pass, show success message
            alert('Thank you for your booking request! We will contact you shortly to confirm your reservation.');
            bookingForm.reset();
        });
    }

    // Newsletter Form
    const newsletterForm = document.querySelector('.newsletter-form');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;

            if (email) {
                alert('Thank you for subscribing to our newsletter!');
                this.reset();
            } else {
                alert('Please enter your email address.');
            }
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add animation on scroll
    const animateElements = document.querySelectorAll('.apartment-card, .amenity-card, .gallery-item');

    const checkIfInView = () => {
        animateElements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementPosition < windowHeight - 100) {
                element.classList.add('animate');
            }
        });
    };

    // Add CSS class for animation
    animateElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    });

    // Add animate class to elements in view
    window.addEventListener('load', checkIfInView);
    window.addEventListener('scroll', checkIfInView);

    // Add animation class to elements in view
    document.addEventListener('scroll', checkIfInView);
    window.addEventListener('resize', checkIfInView);

    // Function to animate elements when they come into view
    function checkIfInView() {
        animateElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }
        });
    }

    // Check if elements are in view on page load
    checkIfInView();
});
